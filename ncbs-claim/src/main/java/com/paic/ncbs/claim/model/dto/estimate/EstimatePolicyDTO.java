package com.paic.ncbs.claim.model.dto.estimate;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel("意键险保单预估")
@JsonIgnoreProperties({"currentPage", "perPageSize", "pageNumEnd", "pageNumBegin", "userId"})
public class EstimatePolicyDTO extends EntityDTO {

    private static final long serialVersionUID = 6385704000113269380L;

    @ApiModelProperty("意键险保单预估主键")
    private String idAhcsEstimatePolicy;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔案号")
    private String caseNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("")
    private Integer subTimes;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("电子保单号")
    private String policyCerNo;

    @ApiModelProperty("预估金额")
    private BigDecimal estimateAmount;

    @ApiModelProperty("仲裁费")
    private BigDecimal arbitrageFee;

    @ApiModelProperty("诉讼费")
    private BigDecimal lawsuitFee;

    @ApiModelProperty("公估费")
    private BigDecimal commonEstimateFee;

    @ApiModelProperty("律师费")
    private BigDecimal lawyerFee;

    @ApiModelProperty("执行费")
    private BigDecimal executeFee;

    @ApiModelProperty("检验及鉴定费")
    private BigDecimal verifyAppraiseFee;

     //新增
     @ApiModelProperty("调查费")
     private BigDecimal inquireFee;

    @ApiModelProperty("其他")
    private BigDecimal otherFee;

    @ApiModelProperty("专项查勘费")
    private BigDecimal specialSurveyFee;

    @ApiModelProperty("我司份额")
    private BigDecimal coShare;

    @ApiModelProperty("是否主承保 0否1是")
    private String isPrincipalUnderwriting;

    @ApiModelProperty("保单开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginTime;

    @ApiModelProperty("保单结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndTime;

    @ApiModelProperty("被保人姓名")
    private String name;

    @ApiModelProperty("层级号")
    private String subpolicyNo;

    @ApiModelProperty("被保险人编码")
    private String insuredCode;

    @ApiModelProperty("承保机构")
    private String departmentCode;

    @ApiModelProperty("承保机构名称")
    private String departmentName;

    @ApiModelProperty("客户号")
    private String partyNo;

    @ApiModelProperty("EstimatePlanDTO-预估险种DTO")
    private List<EstimatePlanDTO> estimatePlanList;

    @ApiModelProperty("保单赔案关系表")
    private PolicyClaimCaseDTO policyClaimCaseDTO;

    @ApiModelProperty("共保属性 0系统外共保 1系统内共保")
    private String coinsuranceType;

    @ApiModelProperty("标的共享保额,1：是，0：否")
    private String shareInsuredAmount;

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("版本号")
    private String productVersion;

    @ApiModelProperty("")
    private String isProductCodeNoMaxPay;
    @ApiModelProperty("共保展示描述文字")
    private String coinsuranceDesc;

    @ApiModelProperty(value = "标的组列表")
    private List<RiskGroupDTO> riskGroupList;

    private boolean rollback;

    private String riskGroupName;

    private String riskGroupNo;

}
