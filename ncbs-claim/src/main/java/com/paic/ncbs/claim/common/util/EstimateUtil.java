package com.paic.ncbs.claim.common.util;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.TacheConstants;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.user.UserDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;

import java.math.BigDecimal;
import java.util.*;

public class EstimateUtil {


    public static final String YES_REGISTR = "Y";

    public static final String NO_REGISTR = "N";

    public static final String ESTIMATE_TYPE_ESTIMATE = "01";


    public static final String ESTIMATE_TYPE_ESTIMATE_NAME = "预估未决";


    public static final String ESTIMATE_TYPE_REGISTRATION = "02";


    public static final String ESTIMATE_TYPE_REGISTRATION_NAME = "立案未决";


    public static final String ESTIMATE_TYPE_TRACK = "03";


    public static final String ESTIMATE_TYPE_TRACK_NAME = "跟踪未决";



    public static final String ESTIMATE_TYPE_CHECKLOSS = "04";


    public static final String ESTIMATE_TYPE_CHECKLOSS_NAME = "核赔未决";

    public static final String ESTIMATE_TYPE_RESTART = "05";


    public static final String ESTIMATE_TYPE_RESTART_NAME = "重开未决";


    public static void validEstimateDataList(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException {
        if (ListUtils.isNotEmpty(estimatePolicyFormDTO.getEstimatePolicyList())) {
            for (com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO EstimatePolicyDTO : estimatePolicyFormDTO.getEstimatePolicyList()) {
                validEstimatePolicyDTO(EstimatePolicyDTO);
            }
        }
    }


    public static void validEstimatePolicyDTO(EstimatePolicyDTO estimatePolicyDTO) throws GlobalBusinessException {
        if (ListUtils.isNotEmpty(estimatePolicyDTO.getEstimatePlanList())) {
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                if (ListUtils.isNotEmpty(estimatePlanDTO.getEstimateDutyList())) {
                    for (EstimateDutyDTO estimateDutyDTO : estimatePlanDTO.getEstimateDutyList()) {
                        validEstimateDutyDTO(estimateDutyDTO);
                    }
                }
            }


        }
    }


    public static void validPolicyClaimCaseDTO(PolicyClaimCaseDTO policyClaimCaseDTO) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(policyClaimCaseDTO.getCaseNo())
                || StringUtils.isEmptyStr(policyClaimCaseDTO.getPolicyNo())
                || StringUtils.isEmptyStr(policyClaimCaseDTO.getPartyNo())
                || StringUtils.isEmptyStr(policyClaimCaseDTO.getReportNo())
                || StringUtils.isEmptyStr(policyClaimCaseDTO.getDepartmentCode())
                || StringUtils.isEmptyStr(policyClaimCaseDTO.getSubpolicyNo())) {
            LogUtil.audit("参数为空或者类型不对" + ":" + policyClaimCaseDTO.toString());
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
        }
    }


    public static void validEstimatePlanDTO(EstimatePlanDTO estimatePlanDTO) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(estimatePlanDTO.getCaseNo())
                || StringUtils.isEmptyStr(estimatePlanDTO.getPolicyNo())
                || StringUtils.isEmptyStr(estimatePlanDTO.getPlanCode())) {
            LogUtil.audit("#立案模块#检验#检验EstimatePlanDTO#参数为空或者类型不对" + ":" + estimatePlanDTO.toString());
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
        }
    }


    public static void validEstimateDutyDTO(EstimateDutyDTO estimateDutyDTO) throws GlobalBusinessException {

        if (BigDecimalUtils.compareBigDecimalMinus(estimateDutyDTO.getDutyMaxPay(), estimateDutyDTO.getEstimateAmount())) {
            LogUtil.audit("#立案模块#检验EstimateDutyDTO#责任最大给付额大于其基本保额" + ":" + estimateDutyDTO.toString());
            throw new GlobalBusinessException(ErrorCode.Estimate.MXX_PAY_GREATER_THAN_BASE_AMOUNT);
        }

    }


    public static boolean isContainPolicyClaimCaseDTOList(EstimatePolicyDTO estimatePolicyDTO,
                                                          List<PolicyClaimCaseDTO> policyClaimCaseDTOList) {
        if (ListUtils.isEmptyList(policyClaimCaseDTOList)) {
            return false;
        }
        Iterator<PolicyClaimCaseDTO> it = policyClaimCaseDTOList.iterator();
        while (it.hasNext()) {
            PolicyClaimCaseDTO policyClaimCase = it.next();
            if (policyClaimCase.getCaseNo().equals(estimatePolicyDTO.getCaseNo())) {
                return true;
            }
        }
        return false;
    }


    public static void handleEstimatePolicyFormDTO(EstimatePolicyFormDTO estimatePolicyFormDTO, UserDTO userDTO)
            throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(userDTO.getUserId())) {
            LogUtil.audit("userDTO.getUserId()参数为空或者类型不对" + ":" + userDTO.getUserId());
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
        }
        if (estimatePolicyFormDTO == null) {
            LogUtil.audit("estimatePolicyFormDTO参数为空或者类型不对");
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
        }
        if (ListUtils.isNotEmpty(estimatePolicyFormDTO.getEstimatePolicyList())) {
            for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyFormDTO.getEstimatePolicyList()) {
                if (ListUtils.isNotEmpty(estimatePolicyDTO.getEstimatePlanList())) {
                    for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                        if (ListUtils.isEmptyList(estimatePlanDTO.getEstimateDutyRecordList())) {
                            LogUtil.audit("estimatePlanDTO.getEstimateDutyRecordList()参数为空或者类型不对");
                            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
                        }
                    }
                } else {
                    LogUtil.audit("estimatePolicyDTO.getEstimatePlanList()参数为空或者类型不对");
                    throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
                }
            }
        } else {
            LogUtil.audit("estimatePolicyFormDTO.getEstimatePolicyList()参数为空或者类型不对");
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR);
        }
        estimatePolicyFormDTO.setUpdatedBy(userDTO.getUserId());
    }

    public static List<EstimateDutyHistoryDTO> convertToHistoryDTO(List<EstimateDutyDTO> estimateDutyDTOList, String updatedBy, String migrateFrom) {
        List<EstimateDutyHistoryDTO> estimateDutyHistoryDTOList = new ArrayList<EstimateDutyHistoryDTO>();
        for (EstimateDutyDTO estimateDutyDTO : estimateDutyDTOList) {
            String uuid = UuidUtil.getUUID();
            EstimateDutyHistoryDTO estimateDutyHistoryDTO = new EstimateDutyHistoryDTO();
            estimateDutyHistoryDTO.setIdAhcsEstimatDutyHistory(uuid);
            estimateDutyHistoryDTO.setCreatedBy(estimateDutyDTO.getCreatedBy());
            if(null == updatedBy){
                estimateDutyHistoryDTO.setUpdatedBy(estimateDutyDTO.getUpdatedBy());
            }else{
                estimateDutyHistoryDTO.setUpdatedBy(updatedBy);
            }
            estimateDutyHistoryDTO.setPolicyNo(estimateDutyDTO.getPolicyNo());
            estimateDutyHistoryDTO.setCaseNo(estimateDutyDTO.getCaseNo());
            estimateDutyHistoryDTO.setCaseTimes(estimateDutyDTO.getCaseTimes());
            estimateDutyHistoryDTO.setPlanCode(estimateDutyDTO.getPlanCode());
            estimateDutyHistoryDTO.setDutyCode(estimateDutyDTO.getDutyCode());
            estimateDutyHistoryDTO.setDutyName(estimateDutyDTO.getDutyName());
            estimateDutyHistoryDTO.setBaseAmountPay(estimateDutyDTO.getBaseAmountPay());
            estimateDutyHistoryDTO.setDutyEstimateAmount(estimateDutyDTO.getEstimateAmount());
            if(ObjectUtils.isNotNull(migrateFrom)) {
                estimateDutyHistoryDTO.setMigrateFrom(migrateFrom);
            }
            estimateDutyHistoryDTO.setArbitrageFee(estimateDutyDTO.getArbitrageFee());
            estimateDutyHistoryDTO.setLawsuitFee(estimateDutyDTO.getLawsuitFee());
            estimateDutyHistoryDTO.setCommonEstimateFee(estimateDutyDTO.getCommonEstimateFee());
            estimateDutyHistoryDTO.setLawyerFee(estimateDutyDTO.getLawyerFee());
            estimateDutyHistoryDTO.setExecuteFee(estimateDutyDTO.getExecuteFee());
            estimateDutyHistoryDTO.setVerifyAppraiseFee(estimateDutyDTO.getVerifyAppraiseFee());
            estimateDutyHistoryDTO.setInquireFee(estimateDutyDTO.getInquireFee());
            estimateDutyHistoryDTO.setOtherFee(estimateDutyDTO.getOtherFee());
            estimateDutyHistoryDTO.setSpecialSurveyFee(estimateDutyDTO.getSpecialSurveyFee());
            estimateDutyHistoryDTO.setEstimateType(estimateDutyDTO.getEstimateType());
            estimateDutyHistoryDTO.setIdPlyRiskProperty(estimateDutyDTO.getIdPlyRiskProperty());
            estimateDutyHistoryDTO.setRiskGroupNo(estimateDutyDTO.getRiskGroupNo());
            estimateDutyHistoryDTO.setRiskGroupName(estimateDutyDTO.getRiskGroupName());
            estimateDutyHistoryDTOList.add(estimateDutyHistoryDTO);
        }
        return estimateDutyHistoryDTOList;
    }

    public static BigDecimal getEstimateAmountSum(List<EstimatePolicyDTO> policyList) {

        List<BigDecimal> policySum = new ArrayList<>();
        if (policyList == null){
            return BigDecimal.ZERO;
        }

        for (EstimatePolicyDTO estimatePolicyDTO : policyList) {
            BigDecimal policy = new BigDecimal("0");

            if (null != estimatePolicyDTO.getEstimateAmount()) {
                policy = policy.add(estimatePolicyDTO.getEstimateAmount());
            }
            if (null != estimatePolicyDTO.getArbitrageFee()) {
                policy = policy.add(estimatePolicyDTO.getArbitrageFee());
            }
            if (null != estimatePolicyDTO.getLawsuitFee()) {
                policy = policy.add(estimatePolicyDTO.getLawsuitFee());
            }
            if (null != estimatePolicyDTO.getCommonEstimateFee()) {
                policy = policy.add(estimatePolicyDTO.getCommonEstimateFee());
            }
            if (null != estimatePolicyDTO.getLawyerFee()) {
                policy = policy.add(estimatePolicyDTO.getLawyerFee());
            }
            if (null != estimatePolicyDTO.getExecuteFee()) {
                policy = policy.add(estimatePolicyDTO.getExecuteFee());
            }
            if (null != estimatePolicyDTO.getVerifyAppraiseFee()) {
                policy = policy.add(estimatePolicyDTO.getVerifyAppraiseFee());
            }
            if (null != estimatePolicyDTO.getOtherFee()) {
                policy = policy.add(estimatePolicyDTO.getOtherFee());
            }
            if (null != estimatePolicyDTO.getSpecialSurveyFee()) {
                policy = policy.add(estimatePolicyDTO.getSpecialSurveyFee());
            }
            if (null != estimatePolicyDTO.getInquireFee()) {
                policy = policy.add(estimatePolicyDTO.getInquireFee());
            }

            if (null != estimatePolicyDTO.getCoShare() & BigDecimalUtils.isGreaterZero(policy)) {

                policy = estimatePolicyDTO.getCoShare().multiply(policy).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            policySum.add(policy);
        }
        BigDecimal amount = BigDecimalUtils.sum(policySum);
        amount.setScale(2, BigDecimal.ROUND_HALF_UP);
        return amount;
    }


    public static BigDecimal getEstimateAmountSum(EstimatePolicyDTO estimatePolicyDTO) {

        BigDecimal policySum = new BigDecimal("0");

        policySum = policySum.add(estimatePolicyDTO.getEstimateAmount());
        policySum = policySum.add(estimatePolicyDTO.getArbitrageFee());
        policySum = policySum.add(estimatePolicyDTO.getLawsuitFee());
        policySum = policySum.add(estimatePolicyDTO.getCommonEstimateFee());
        policySum = policySum.add(estimatePolicyDTO.getLawyerFee());
        policySum = policySum.add(estimatePolicyDTO.getExecuteFee());
        policySum = policySum.add(estimatePolicyDTO.getVerifyAppraiseFee());
        policySum = policySum.add(estimatePolicyDTO.getInquireFee());
        policySum = policySum.add(estimatePolicyDTO.getOtherFee());
        policySum = policySum.add(estimatePolicyDTO.getSpecialSurveyFee());
        return BigDecimalUtils.sum(policySum);
    }


    public static BigDecimal getEstimateAmountSumByDutyRecord(List<EstimatePolicyDTO> policyList) {
        List<BigDecimal> policySum = new ArrayList<>();

        for (EstimatePolicyDTO estimatePolicyDTO : policyList) {
            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                for (EstimateDutyRecordDTO estimateDutyRecordDTO : estimatePlanDTO.getEstimateDutyRecordList()) {
                    policySum.add(estimateDutyRecordDTO.getEstimateAmount());
                    policySum.add(estimateDutyRecordDTO.getArbitrageFee());
                    policySum.add(estimateDutyRecordDTO.getLawsuitFee());
                    policySum.add(estimateDutyRecordDTO.getCommonEstimateFee());
                    policySum.add(estimateDutyRecordDTO.getLawyerFee());
                    policySum.add(estimateDutyRecordDTO.getExecuteFee());
                    policySum.add(estimateDutyRecordDTO.getVerifyAppraiseFee());
                    policySum.add(estimateDutyRecordDTO.getInquireFee());
                    policySum.add(estimateDutyRecordDTO.getOtherFee());
                    policySum.add(estimateDutyRecordDTO.getSpecialSurveyFee());
                }
            }
        }
        return BigDecimalUtils.sum(policySum);
    }

    /**
     * 计算保单维度赔款和费用 总金额
     * @param policyPaySumMap
     * @param policyFeeSumMap
     * @param estimateDutyRecordDTO
     */
    public static void calPolicyPayAndFeeSumAmount(Map<String, BigDecimal> policyPaySumMap, Map<String, BigDecimal> policyFeeSumMap, EstimateDutyRecordDTO estimateDutyRecordDTO) {
        BigDecimal existPaySum = new BigDecimal(0);
        BigDecimal existFeeSum = new BigDecimal(0);
        if (policyPaySumMap.containsKey(estimateDutyRecordDTO.getPolicyNo())) {
            existPaySum = policyPaySumMap.get(estimateDutyRecordDTO.getPolicyNo());
        }
        if (policyFeeSumMap.containsKey(estimateDutyRecordDTO.getPolicyNo())) {
            existFeeSum = policyFeeSumMap.get(estimateDutyRecordDTO.getPolicyNo());
        }
        existPaySum = existPaySum.add(estimateDutyRecordDTO.getEstimateAmount()==null?new BigDecimal(0): estimateDutyRecordDTO.getEstimateAmount());
        existFeeSum = existFeeSum.add(estimateDutyRecordDTO.getArbitrageFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getArbitrageFee())
                .add(estimateDutyRecordDTO.getLawsuitFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawsuitFee())
                .add(estimateDutyRecordDTO.getCommonEstimateFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getCommonEstimateFee())
                .add(estimateDutyRecordDTO.getLawyerFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawyerFee())
                .add(estimateDutyRecordDTO.getExecuteFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getExecuteFee())
                .add(estimateDutyRecordDTO.getVerifyAppraiseFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getVerifyAppraiseFee())
                .add(estimateDutyRecordDTO.getInquireFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getInquireFee())
                .add(estimateDutyRecordDTO.getSpecialSurveyFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getSpecialSurveyFee())
                .add(estimateDutyRecordDTO.getOtherFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getOtherFee());
        policyPaySumMap.put(estimateDutyRecordDTO.getPolicyNo(),existPaySum);
        policyFeeSumMap.put(estimateDutyRecordDTO.getPolicyNo(),existFeeSum);
    }


    public static BigDecimal getEstimateAmountSumByDutyHistory(List<EstimateDutyHistoryDTO> dutyHistoryList, Map<String, BigDecimal> policyClaimPercentMap) {

        BigDecimal reportAmount = BigDecimal.ZERO;
        for (String caseNo : policyClaimPercentMap.keySet()) {

            BigDecimal policyAmout = BigDecimal.ZERO;
            for (EstimateDutyHistoryDTO estimateDutyHistory : dutyHistoryList) {
                List<BigDecimal> policySum = new ArrayList<>();
                if (caseNo.equals(estimateDutyHistory.getCaseNo())) {
                    policySum.add(estimateDutyHistory.getDutyEstimateAmount());
                    policySum.add(estimateDutyHistory.getArbitrageFee());
                    policySum.add(estimateDutyHistory.getLawsuitFee());
                    policySum.add(estimateDutyHistory.getCommonEstimateFee());
                    policySum.add(estimateDutyHistory.getLawyerFee());
                    policySum.add(estimateDutyHistory.getExecuteFee());
                    policySum.add(estimateDutyHistory.getVerifyAppraiseFee());
                    policySum.add(estimateDutyHistory.getInquireFee());
                    policySum.add(estimateDutyHistory.getOtherFee());
                    policySum.add(estimateDutyHistory.getSpecialSurveyFee());
                    policyAmout = policyAmout.add(BigDecimalUtils.sum(policySum));
                }
            }

            if (policyClaimPercentMap.get(caseNo) != null) {
                policyAmout = policyAmout.multiply(policyClaimPercentMap.get(caseNo)).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            reportAmount = reportAmount.add(policyAmout);
        }
        return reportAmount;
    }


    public static BigDecimal getEstimateDutyAmountSum(Object object) {

        List<BigDecimal> bds = new ArrayList<>();

        if (object instanceof EstimateDutyDTO) {
            EstimateDutyDTO estimateDutyDTO = (EstimateDutyDTO) object;
            bds.add(estimateDutyDTO.getEstimateAmount());
            bds.add(estimateDutyDTO.getArbitrageFee());
            bds.add(estimateDutyDTO.getLawsuitFee());
            bds.add(estimateDutyDTO.getCommonEstimateFee());
            bds.add(estimateDutyDTO.getLawyerFee());
            bds.add(estimateDutyDTO.getExecuteFee());
            bds.add(estimateDutyDTO.getVerifyAppraiseFee());
            bds.add(estimateDutyDTO.getInquireFee());
            bds.add(estimateDutyDTO.getOtherFee());
            bds.add(estimateDutyDTO.getSpecialSurveyFee());
        } else {
            EstimateDutyRecordDTO estimateDutyRecordDTO = (EstimateDutyRecordDTO) object;
            bds.add(estimateDutyRecordDTO.getEstimateAmount());
            bds.add(estimateDutyRecordDTO.getArbitrageFee());
            bds.add(estimateDutyRecordDTO.getLawsuitFee());
            bds.add(estimateDutyRecordDTO.getCommonEstimateFee());
            bds.add(estimateDutyRecordDTO.getLawyerFee());
            bds.add(estimateDutyRecordDTO.getExecuteFee());
            bds.add(estimateDutyRecordDTO.getVerifyAppraiseFee());
            bds.add(estimateDutyRecordDTO.getInquireFee());
            bds.add(estimateDutyRecordDTO.getOtherFee());
            bds.add(estimateDutyRecordDTO.getSpecialSurveyFee());
        }

        return BigDecimalUtils.sum(bds);
    }


    public static List<EstimateDutyRecordDTO> convertToEsitmateDutyRecordDTO(List<EstimateDutyDTO> estimateDutyDTOList, Map<String, String> paramMap) {
        List<EstimateDutyRecordDTO> estimateDutyRecordDTOList = new ArrayList<EstimateDutyRecordDTO>();
        for (EstimateDutyDTO estimateDutyDTO : estimateDutyDTOList) {
            EstimateDutyRecordDTO estimateDutyRecordDTO = new EstimateDutyRecordDTO();
            if (StringUtils.isEmptyStr(estimateDutyDTO.getCreatedBy())) {
                estimateDutyRecordDTO.setCreatedBy("system");
            } else {
                estimateDutyRecordDTO.setCreatedBy(estimateDutyDTO.getCreatedBy());
            }
            estimateDutyRecordDTO.setCreatedDate(estimateDutyDTO.getCreatedDate());
            if (StringUtils.isEmptyStr(estimateDutyDTO.getCreatedBy())) {
                estimateDutyRecordDTO.setUpdatedBy("system");
            } else {
                estimateDutyRecordDTO.setUpdatedBy(estimateDutyDTO.getCreatedBy());
            }
            estimateDutyRecordDTO.setUpdatedDate(estimateDutyDTO.getUpdatedDate());
            estimateDutyRecordDTO.setIdAhcsEstimateDutyRecord(estimateDutyDTO.getIdAhcsEstimateDuty());
            estimateDutyRecordDTO.setTaskId(TacheConstants.ESTIMATE);
            estimateDutyRecordDTO.setPolicyNo(paramMap.get("policyNo"));
            estimateDutyRecordDTO.setCaseNo(paramMap.get("caseNo"));
            estimateDutyRecordDTO.setCaseTimes(Integer.parseInt(paramMap.get("caseTimes")));
            estimateDutyRecordDTO.setPlanCode(paramMap.get("planCode"));
            estimateDutyRecordDTO.setDutyCode(estimateDutyDTO.getDutyCode());
            estimateDutyRecordDTO.setDutyName(estimateDutyDTO.getDutyName());
            estimateDutyRecordDTO.setBaseAmountPay(estimateDutyDTO.getBaseAmountPay());
            estimateDutyRecordDTO.setEstimateAmount(estimateDutyDTO.getEstimateAmount());
            estimateDutyRecordDTO.setArbitrageFee(estimateDutyDTO.getArbitrageFee());
            estimateDutyRecordDTO.setLawsuitFee(estimateDutyDTO.getLawsuitFee());
            estimateDutyRecordDTO.setCommonEstimateFee(estimateDutyDTO.getCommonEstimateFee());
            estimateDutyRecordDTO.setLawyerFee(estimateDutyDTO.getLawyerFee());
            estimateDutyRecordDTO.setExecuteFee(estimateDutyDTO.getExecuteFee());
            estimateDutyRecordDTO.setVerifyAppraiseFee(estimateDutyDTO.getVerifyAppraiseFee());
            estimateDutyRecordDTO.setInquireFee(estimateDutyDTO.getInquireFee());
            estimateDutyRecordDTO.setOtherFee(estimateDutyDTO.getOtherFee());
            estimateDutyRecordDTO.setSpecialSurveyFee(estimateDutyDTO.getSpecialSurveyFee());
            estimateDutyRecordDTO.setEstimateType(paramMap.get("estimateType"));
            estimateDutyRecordDTO.setIdPlyRiskProperty(estimateDutyDTO.getIdPlyRiskProperty());
            estimateDutyRecordDTO.setRiskGroupNo(estimateDutyDTO.getRiskGroupNo());
            estimateDutyRecordDTO.setRiskGroupName(estimateDutyDTO.getRiskGroupName());
            estimateDutyRecordDTO.setChgPayValue(estimateDutyDTO.getEstimateAmount());
            estimateDutyRecordDTOList.add(estimateDutyRecordDTO);
        }
        return estimateDutyRecordDTOList;
    }


    public static EstimateDutyDTO covertToEstimateDutyDTO(EstimateDutyDTO estimateDutyDTO, EstimateDutyRecordDTO estimateDutyRecordDTO) {
        estimateDutyDTO.setCreatedBy("system");
        estimateDutyDTO.setUpdatedBy("system");
        estimateDutyDTO.setIdAhcsEstimateDuty(estimateDutyRecordDTO.getIdAhcsEstimateDutyRecord());
        estimateDutyDTO.setPolicyNo(estimateDutyRecordDTO.getPolicyNo());
        estimateDutyDTO.setCaseNo(estimateDutyRecordDTO.getCaseNo());
        estimateDutyDTO.setCaseTimes(estimateDutyRecordDTO.getCaseTimes());
        estimateDutyDTO.setPlanCode(estimateDutyRecordDTO.getPlanCode());
        estimateDutyDTO.setDutyCode(estimateDutyRecordDTO.getDutyCode());
        estimateDutyDTO.setDutyName(estimateDutyRecordDTO.getDutyName());
        estimateDutyDTO.setBaseAmountPay(estimateDutyRecordDTO.getBaseAmountPay());
        estimateDutyDTO.setEstimateAmount(estimateDutyRecordDTO.getEstimateAmount());
        estimateDutyDTO.setArbitrageFee(estimateDutyRecordDTO.getArbitrageFee());
        estimateDutyDTO.setLawsuitFee(estimateDutyRecordDTO.getLawsuitFee());
        estimateDutyDTO.setCommonEstimateFee(estimateDutyRecordDTO.getCommonEstimateFee());
        estimateDutyDTO.setLawyerFee(estimateDutyRecordDTO.getLawyerFee());
        estimateDutyDTO.setExecuteFee(estimateDutyRecordDTO.getExecuteFee());
        estimateDutyDTO.setVerifyAppraiseFee(estimateDutyRecordDTO.getVerifyAppraiseFee());
        estimateDutyDTO.setInquireFee(estimateDutyRecordDTO.getInquireFee());
        estimateDutyDTO.setOtherFee(estimateDutyRecordDTO.getOtherFee());
        estimateDutyDTO.setSpecialSurveyFee(estimateDutyRecordDTO.getSpecialSurveyFee());
        estimateDutyDTO.setEstimateType(estimateDutyRecordDTO.getEstimateType());
        return estimateDutyDTO;
    }


    public static void setPlanAmount(EstimatePlanDTO planDTO, List<EstimateDutyRecordDTO> dutyRecordDTOS){
        BigDecimal estimateAmountSum = null;
        BigDecimal arbitrageFeeSum = null;
        BigDecimal lawsuitFeeSum = null;
        BigDecimal commonEstimateFeeSum = null;
        BigDecimal lawyerFeeSum = null;
        BigDecimal executeFeeSum = null;
        BigDecimal verifyFeeSum = null;
        BigDecimal inquireFeeSum = null;
        BigDecimal otherFeeSum = null;
        BigDecimal specialSurveyFeeSum = null;



        for(EstimateDutyRecordDTO item : dutyRecordDTOS){
            if(item.getEstimateAmount()!=null){
                estimateAmountSum = estimateAmountSum==null? item.getEstimateAmount() : estimateAmountSum.add(item.getEstimateAmount());
            }

            if(item.getArbitrageFee()!=null){
                arbitrageFeeSum = arbitrageFeeSum==null? item.getArbitrageFee():arbitrageFeeSum.add(item.getArbitrageFee());
            }

            if(item.getLawsuitFee()!=null){
                lawsuitFeeSum = lawsuitFeeSum==null? item.getLawsuitFee():lawsuitFeeSum.add(item.getLawsuitFee());
            }

            if(item.getCommonEstimateFee()!=null){
                commonEstimateFeeSum = commonEstimateFeeSum==null? item.getCommonEstimateFee():commonEstimateFeeSum.add(item.getCommonEstimateFee());
            }

            if(item.getLawyerFee()!=null){
                lawyerFeeSum = lawyerFeeSum==null? item.getLawyerFee():lawyerFeeSum.add(item.getLawyerFee());
            }

            if(item.getExecuteFee()!=null){
                executeFeeSum = executeFeeSum==null? item.getExecuteFee():executeFeeSum.add(item.getExecuteFee());
            }

            if(item.getVerifyAppraiseFee()!=null){
                verifyFeeSum = verifyFeeSum==null? item.getVerifyAppraiseFee():verifyFeeSum.add(item.getVerifyAppraiseFee());
            }

            if(item.getInquireFee()!=null){
                inquireFeeSum = inquireFeeSum==null? item.getInquireFee():inquireFeeSum.add(item.getInquireFee());
            }
            if(item.getOtherFee()!=null){
                otherFeeSum = otherFeeSum==null? item.getOtherFee():otherFeeSum.add(item.getOtherFee());
            }
            if(item.getSpecialSurveyFee()!=null){
                specialSurveyFeeSum = specialSurveyFeeSum==null? item.getSpecialSurveyFee():specialSurveyFeeSum.add(item.getSpecialSurveyFee());
            }
        }
        planDTO.setEstimateAmount(estimateAmountSum);
        planDTO.setArbitrageFee(arbitrageFeeSum);
        planDTO.setLawsuitFee(lawsuitFeeSum);
        planDTO.setCommonEstimateFee(commonEstimateFeeSum);
        planDTO.setLawyerFee(lawyerFeeSum);
        planDTO.setExecuteFee(executeFeeSum);
        planDTO.setVerifyAppraiseFee(verifyFeeSum);
        planDTO.setInquireFee(inquireFeeSum);
        planDTO.setInquireFee(otherFeeSum);
        planDTO.setInquireFee(specialSurveyFeeSum);
    }


    public static void setPolicyAmount(EstimatePolicyDTO policyDTO, List<EstimatePlanDTO> planDTOList){
        BigDecimal estimateAmountSum = null;
        BigDecimal arbitrageFeeSum = null;
        BigDecimal lawsuitFeeSum = null;
        BigDecimal commonEstimateFeeSum = null;
        BigDecimal lawyerFeeSum = null;
        BigDecimal executeFeeSum = null;
        BigDecimal verifyFeeSum = null;

        BigDecimal inquireFeeSum = null;
        BigDecimal otherFeeSum = null;
        BigDecimal specialSurveyFeeSum = null;

        for(EstimatePlanDTO item : planDTOList){
            if(item.getEstimateAmount()!=null){
                estimateAmountSum = estimateAmountSum==null? item.getEstimateAmount() : estimateAmountSum.add(item.getEstimateAmount());
            }

            if(item.getArbitrageFee()!=null){
                arbitrageFeeSum = arbitrageFeeSum==null? item.getArbitrageFee():arbitrageFeeSum.add(item.getArbitrageFee());
            }

            if(item.getLawsuitFee()!=null){
                lawsuitFeeSum = lawsuitFeeSum==null? item.getLawsuitFee():lawsuitFeeSum.add(item.getLawsuitFee());
            }

            if(item.getCommonEstimateFee()!=null){
                commonEstimateFeeSum = commonEstimateFeeSum==null? item.getCommonEstimateFee():commonEstimateFeeSum.add(item.getCommonEstimateFee());
            }

            if(item.getLawyerFee()!=null){
                lawyerFeeSum = lawyerFeeSum==null? item.getLawyerFee():lawyerFeeSum.add(item.getLawyerFee());
            }

            if(item.getExecuteFee()!=null){
                executeFeeSum = executeFeeSum==null? item.getExecuteFee():executeFeeSum.add(item.getExecuteFee());
            }

            if(item.getVerifyAppraiseFee()!=null){
                verifyFeeSum = verifyFeeSum==null? item.getVerifyAppraiseFee():verifyFeeSum.add(item.getVerifyAppraiseFee());
            }

            if(item.getInquireFee()!=null){
                inquireFeeSum = inquireFeeSum==null? item.getInquireFee():inquireFeeSum.add(item.getInquireFee());
            }

            if(item.getOtherFee()!=null){
                otherFeeSum = otherFeeSum==null? item.getOtherFee():otherFeeSum.add(item.getOtherFee());
            }

            if(item.getSpecialSurveyFee()!=null){
                specialSurveyFeeSum = inquireFeeSum==null? item.getSpecialSurveyFee():specialSurveyFeeSum.add(item.getSpecialSurveyFee());
            }
        }
        policyDTO.setEstimateAmount(estimateAmountSum);
        policyDTO.setArbitrageFee(arbitrageFeeSum);
        policyDTO.setLawsuitFee(lawsuitFeeSum);
        policyDTO.setCommonEstimateFee(commonEstimateFeeSum);
        policyDTO.setLawyerFee(lawyerFeeSum);
        policyDTO.setExecuteFee(executeFeeSum);
        policyDTO.setVerifyAppraiseFee(verifyFeeSum);
        policyDTO.setInquireFee(inquireFeeSum);
        policyDTO.setOtherFee(otherFeeSum);
        policyDTO.setSpecialSurveyFee(specialSurveyFeeSum);
    }


    public static BigDecimal getEstimateDutyRecordAmountSum(List<EstimateDutyRecordDTO> dutyRecordDTOList){
        BigDecimal amount = BigDecimal.ZERO;
        if(ListUtils.isEmptyList(dutyRecordDTOList)){
            return amount;
        }
        for(EstimateDutyRecordDTO dutyRecordDTO : dutyRecordDTOList){
            if(dutyRecordDTO.getEstimateAmount()!=null){
                amount = amount.add(dutyRecordDTO.getEstimateAmount());
            }

            if(dutyRecordDTO.getArbitrageFee()!=null){
                amount = amount.add(dutyRecordDTO.getArbitrageFee());
            }

            if(dutyRecordDTO.getLawsuitFee()!=null){
                amount = amount.add(dutyRecordDTO.getLawsuitFee());
            }

            if(dutyRecordDTO.getCommonEstimateFee()!=null){
                amount = amount.add(dutyRecordDTO.getCommonEstimateFee());
            }

            if(dutyRecordDTO.getLawyerFee()!=null){
                amount = amount.add(dutyRecordDTO.getLawyerFee());
            }

            if(dutyRecordDTO.getExecuteFee()!=null){
                amount = amount.add(dutyRecordDTO.getExecuteFee());
            }

            if(dutyRecordDTO.getVerifyAppraiseFee()!=null){
                amount = amount.add(dutyRecordDTO.getVerifyAppraiseFee());
            }

            if(dutyRecordDTO.getInquireFee()!=null){
                amount = amount.add(dutyRecordDTO.getInquireFee());
            }
            if(dutyRecordDTO.getOtherFee()!=null){
                amount = amount.add(dutyRecordDTO.getOtherFee());
            }
            if(dutyRecordDTO.getSpecialSurveyFee()!=null){
                amount = amount.add(dutyRecordDTO.getSpecialSurveyFee());
            }
        }
        return amount;
    }
}
